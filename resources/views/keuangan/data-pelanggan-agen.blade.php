@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pelanggan Agen')

@section('content')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="/dashboard" class="text-decoration-none">Dashboard</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/corp/pendapatan" class="text-decoration-none">Langganan</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/data/pendapatan" class="text-decoration-none">Personal</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/data-agen" class="text-decoration-none">Data Agen</a>
        </li>
        <li class="breadcrumb-item active">Data Pelanggan Agen</li>
    </ol>
</nav>

<div class="row">
    <div class="col-12">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title fw-bold">Data Pelanggan Agen {{ $agen->name }}</h5>
                <small class="card-subtitle">Daftar seluruh pelanggan yang terdaftar di bawah agen {{ $agen->name }}</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark text-center">
                            <tr>
                                <th>No</th>
                                <th>Nama Pelanggan</th>
                                <th>No. HP</th>
                                <th>Status Tagihan</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            @forelse ($pelanggan as $p)
                            <tr class="agen-row" data-id="{{ $p->id }}"
                                data-nama="{{ strtolower($p->nama_customer) }}">
                                <td class="text-center">{{ $loop->iteration }}</td>
                                <td class="agen-name">{{ $p->nama_customer }}</td>
                                <td class="agen-hp">{{ $p->no_hp }}</td>
                                <td>
                                    @forelse ($p->invoice as $inv)
                                        @if ($inv->status)
                                        <span class="badge 
                                            @if($inv->status->id == 1) bg-info text-info
                                            @elseif($inv->status->id == 8) bg-success text-success
                                            @elseif($inv->status->id == 7) bg-danger text-danger
                                            @endif">
                                        {{ $inv->status->nama_status }}
                                        </span>
                                        @else
                                            <span class="badge bg-secondary">Status Tidak Ada</span>
                                        @endif
                                    @empty
                                        <span class="badge bg-secondary">Tidak Ada Invoice</span>
                                    @endforelse
                            </td>
                        </tr>
                        @empty
                        <tr class="empty-state-row">
                            <td colspan="7" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="bx bx-user-x text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="text-dark mt-3 mb-2">Tidak ada data pelanggan</h5>
                                    <p class="text-muted mb-0">Belum ada pelanggan yang terdaftar di bawah agen {{ $agen->name }}</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center mt-3">
                {{ $pelanggan->links() }}
            </div>
        </div>
    </div>
</div>
</div>


@endsection