@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pelanggan Agen')

<style>
    
    .search-container {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        border: 1px solid #e9ecef;
    }
    
    .modern-table {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    
    .modern-table thead th {
        background: #343a40;
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        border: none;
        padding: 1rem 0.75rem;
    }
    
    .modern-table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #e9ecef;
    }
    
    .modern-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .modern-table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border: none;
    }
    
    .customer-name {
        color: #495057;
        font-weight: 600;
    }
    
    .badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }
    
    .btn-group .btn {
        margin: 0 2px;
    }
    
    .empty-state-row td {
        padding: 3rem 1rem;
    }

    .avatar-sm {
        width: 3rem;
        height: 3rem;
        min-width: 3rem;
    }

    @media (max-width: 768px) {
        .modern-table {
            font-size: 0.875rem;
        }

        .modern-table thead th,
        .modern-table tbody td {
            padding: 0.75rem 0.5rem;
        }

        .search-container {
            padding: 1rem;
        }

        .avatar-sm {
            width: 2.5rem;
            height: 2.5rem;
            min-width: 2.5rem;
        }
    }
</style>

@section('content')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="/dashboard" class="text-decoration-none">Dashboard</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/corp/pendapatan" class="text-decoration-none">Langganan</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/data/pendapatan" class="text-decoration-none">Personal</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/data-agen" class="text-decoration-none">Data Agen</a>
        </li>
        <li class="breadcrumb-item active">Data Pelanggan Agen</li>
    </ol>
</nav>

<div class="row">
    <div class="col-12">
        <!-- Header Card -->
        <div class="card mb-3">
            <div class="card-header modern-card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="card-title fw-bold mb-1">Data Invoice Pelanggan Agen {{ $agen->name }}</h4>
                        <small class="card-subtitle text-muted">Daftar seluruh invoice pelanggan yang terdaftar di bawah agen {{ $agen->name }} (setiap invoice ditampilkan terpisah)</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-danger bg-opacity-10 text-danger fs-6 px-3 py-2">
                            <i class="bx bx-receipt me-1"></i>{{ $invoices->total() }} Invoice
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-3">
            <div class="col-md-3 mb-2">
                <div class="card bg-success bg-opacity-10 border-success">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="bx bx-check-circle text-white fs-4"></i>
                            </div>
                            <div>
                                <h4 class="text-success mb-0" id="totalPaid">Rp 0</h4>
                                <small class="text-muted">Total Sudah Bayar</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-2">
                <div class="card bg-danger bg-opacity-10 border-danger">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="avatar-sm bg-danger rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="bx bx-x-circle text-white fs-4"></i>
                            </div>
                            <div>
                                <h4 class="text-danger mb-0" id="totalUnpaid">Rp 0</h4>
                                <small class="text-muted">Total Belum Bayar</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-2">
                <div class="card bg-warning bg-opacity-10 border-warning">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="avatar-sm bg-warning rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="bx bx-time text-white fs-4"></i>
                            </div>
                            <div>
                                <h4 class="text-warning mb-0" id="totalOverdue">Rp 0</h4>
                                <small class="text-muted">Total Terlambat</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-2">
                <div class="card bg-info bg-opacity-10 border-info">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="avatar-sm bg-info rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="bx bx-calculator text-white fs-4"></i>
                            </div>
                            <div>
                                <h4 class="text-info mb-0" id="totalAmount">Rp 0</h4>
                                <small class="text-muted">Total Keseluruhan</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Card -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="search-container">
                    <h6 class="mb-3 fw-bold text-dark">
                        <i class="bx bx-search me-2"></i>Filter & Pencarian Data
                    </h6>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <label class="form-label">Nama Pelanggan</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bx bx-user"></i></span>
                                <input type="text" class="form-control" id="searchName" placeholder="Cari nama pelanggan...">
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label">Filter Periode Bulan</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bx bx-calendar"></i></span>
                                <select class="form-select" id="filterMonth">
                                    <option value="">Semua Bulan</option>
                                    <option value="01">Januari</option>
                                    <option value="02">Februari</option>
                                    <option value="03">Maret</option>
                                    <option value="04">April</option>
                                    <option value="05">Mei</option>
                                    <option value="06">Juni</option>
                                    <option value="07">Juli</option>
                                    <option value="08">Agustus</option>
                                    <option value="09">September</option>
                                    <option value="10">Oktober</option>
                                    <option value="11">November</option>
                                    <option value="12">Desember</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <label class="form-label">Status Tagihan</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bx bx-filter"></i></span>
                                <select class="form-select" id="filterStatus">
                                    <option value="">Semua Status</option>
                                    <option value="Belum Bayar">Belum Bayar</option>
                                    <option value="Sudah Bayar">Sudah Bayar</option>
                                    <option value="Terlambat">Terlambat</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Data Table Card -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table modern-table" id="customerTable">
                        <thead class="table-dark text-center fw-bold">
                            <tr>
                                <th>No</th>
                                <th>Nama Pelanggan</th>
                                <th>Alamat</th>
                                <th>No. HP</th>
                                <th>Paket</th>
                                <th>Status Tagihan</th>
                                <th>Jatuh Tempo</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            @php $rowNumber = ($invoices->currentPage() - 1) * $invoices->perPage() + 1; @endphp
                            @forelse ($invoices as $invoice)
                            <tr class="customer-row" data-id="{{ $invoice->customer->id }}"
                                data-nama="{{ strtolower($invoice->customer->nama_customer) }}"
                                data-alamat="{{ strtolower($invoice->customer->alamat) }}"
                                data-hp="{{ $invoice->customer->no_hp }}"
                                data-paket="{{ $invoice->customer->paket ? strtolower($invoice->customer->paket->nama_paket) : '' }}"
                                data-jatuh-tempo="{{ $invoice->jatuh_tempo ? $invoice->jatuh_tempo : '' }}"
                                data-tagihan="{{ $invoice->tagihan ?? 0 }}"
                                data-status="{{ $invoice->status ? strtolower($invoice->status->nama_status) : '' }}">
                                <td class="text-center">{{ $rowNumber++ }}</td>
                                <td class="customer-name fw-bold">{{ $invoice->customer->nama_customer }}</td>
                                <td class="customer-address">{{ $invoice->customer->alamat }}</td>
                                <td class="customer-phone">{{ $invoice->customer->no_hp }}</td>
                                <td>
                                    @if($invoice->customer->paket)
                                    <span class="badge bg-warning bg-opacity-10 text-warning">
                                        {{ $invoice->customer->paket->nama_paket }}
                                    </span>
                                    @else
                                    <span class="badge bg-secondary bg-opacity-10 text-secondary">Tidak Ada Paket</span>
                                    @endif
                                </td>
                                <td>
                                    @if($invoice->status)
                                    <span class="badge
                                            @if($invoice->status->id == 1) bg-info bg-opacity-10 text-info
                                            @elseif($invoice->status->id == 8) bg-success bg-opacity-10 text-success
                                            @elseif($invoice->status->id == 7) bg-danger bg-opacity-10 text-danger
                                            @else bg-secondary bg-opacity-10 text-secondary
                                            @endif">
                                    {{ $invoice->status->nama_status }}
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary">Tidak Ada Status</span>
                                @endif
                            </td>
                            <td>
                                @if($invoice->jatuh_tempo)
                                @php
                                try {
                                    $jatuhTempo = \Carbon\Carbon::parse($invoice->jatuh_tempo);
                                    $isOverdue = $jatuhTempo->isPast() && $invoice->status && $invoice->status->nama_status != 'Sudah Bayar';
                                } catch (\Exception $e) {
                                    $jatuhTempo = null;
                                    $isOverdue = false;
                                }
                                @endphp
                                @if($jatuhTempo)
                                <span class="badge {{ $isOverdue ? 'bg-danger bg-opacity-10 text-danger' : ($invoice->status && $invoice->status->nama_status == 'Sudah Bayar' ? 'bg-success bg-opacity-10 text-success' : 'bg-info bg-opacity-10 text-info') }}">
                                    {{ $jatuhTempo->format('d M Y') }}
                                    @if($isOverdue)
                                    <br><small>Terlambat</small>
                                    @elseif($invoice->status && $invoice->status->nama_status == 'Sudah Bayar')
                                    <br><small>Lunas</small>
                                    @endif
                                </span>
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary">Invalid Date</span>
                                @endif
                                @else
                                <span class="badge bg-secondary bg-opacity-10 text-secondary">N/A</span>
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr class="empty-state-row">
                            <td colspan="8" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="bx bx-receipt text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="text-dark mt-3 mb-2">Tidak ada data invoice</h5>
                                    <p class="text-muted mb-0">Belum ada invoice untuk pelanggan di bawah agen {{ $agen->name }}</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            @if($invoices->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $invoices->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
</div>



<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchName = document.getElementById('searchName');
        const filterMonth = document.getElementById('filterMonth');
        const filterStatus = document.getElementById('filterStatus');
        const customerRows = document.querySelectorAll('.customer-row');
        const emptyStateRow = document.querySelector('.empty-state-row');
        
        function filterTable() {
            const nameQuery = searchName.value.toLowerCase();
            const monthQuery = filterMonth.value;
            const statusQuery = filterStatus.value.toLowerCase();

            let visibleRows = 0;
            let totalPaid = 0;
            let totalUnpaid = 0;
            let totalOverdue = 0;
            let totalAmount = 0;

            customerRows.forEach(row => {
                const name = row.dataset.nama || '';
                const alamat = row.dataset.alamat || '';
                const jatuhTempo = row.dataset.jatuhTempo || '';
                const tagihan = parseFloat(row.dataset.tagihan || 0);
                const status = row.dataset.status || '';
                const statusBadge = row.querySelector('td:nth-child(6) .badge');
                const statusText = statusBadge ? statusBadge.textContent.toLowerCase() : '';

                // Check name match
                const matchesName = name.includes(nameQuery) || alamat.includes(nameQuery);

                // Check month match
                let matchesMonth = true;
                if (monthQuery && jatuhTempo) {
                    try {
                        const invoiceDate = new Date(jatuhTempo);
                        const invoiceMonth = String(invoiceDate.getMonth() + 1).padStart(2, '0');
                        matchesMonth = invoiceMonth === monthQuery;
                    } catch (e) {
                        matchesMonth = false;
                    }
                }

                // Check status match
                const matchesStatus = !statusQuery || statusText.includes(statusQuery);

                if (matchesName && matchesMonth && matchesStatus) {
                    row.style.display = '';
                    visibleRows++;

                    // Calculate statistics for visible rows
                    totalAmount += tagihan;

                    if (status.includes('sudah bayar')) {
                        totalPaid += tagihan;
                    } else {
                        totalUnpaid += tagihan;

                        // Check if overdue
                        if (jatuhTempo) {
                            try {
                                const dueDate = new Date(jatuhTempo);
                                const today = new Date();
                                if (dueDate < today) {
                                    totalOverdue += tagihan;
                                }
                            } catch (e) {
                                // Ignore date parsing errors
                            }
                        }
                    }
                } else {
                    row.style.display = 'none';
                }
            });

            // Update statistics cards
            updateStatistics(totalPaid, totalUnpaid, totalOverdue, totalAmount);

            // Show/hide empty state
            if (emptyStateRow) {
                if (visibleRows === 0 && customerRows.length > 0) {
                    emptyStateRow.style.display = '';
                    emptyStateRow.querySelector('h5').textContent = 'Tidak ada data yang cocok';
                    emptyStateRow.querySelector('p').textContent = 'Coba ubah kriteria pencarian Anda';
                } else {
                    emptyStateRow.style.display = 'none';
                }
            }
        }

        function updateStatistics(paid, unpaid, overdue, total) {
            document.getElementById('totalPaid').textContent = formatCurrency(paid);
            document.getElementById('totalUnpaid').textContent = formatCurrency(unpaid);
            document.getElementById('totalOverdue').textContent = formatCurrency(overdue);
            document.getElementById('totalAmount').textContent = formatCurrency(total);
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }
        
        // Add event listeners
        searchName.addEventListener('input', filterTable);
        filterMonth.addEventListener('change', filterTable);
        filterStatus.addEventListener('change', filterTable);

        // ESC key to reset filters
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                searchName.value = '';
                filterMonth.value = '';
                filterStatus.value = '';
                filterTable();
            }
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initial calculation of statistics
        filterTable();
    });
</script>

@endsection